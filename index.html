<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Image Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/feather-icons/4.29.0/feather.min.css" rel="stylesheet">
</head>
<body>
    <!-- Welcome Screen -->
    <div id="welcome-screen" class="screen active">
        <div class="container">
            <div class="welcome-content">
                <div class="logo">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="4" y="4" width="40" height="40" rx="8" fill="#6366f1" fill-opacity="0.1"/>
                        <path d="M16 20L24 16L32 20V32C32 33.1046 31.1046 34 30 34H18C16.8954 34 16 33.1046 16 32V20Z" stroke="#6366f1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M20 28C20 26.8954 20.8954 26 22 26H26C27.1046 26 28 26.8954 28 28V34H20V28Z" stroke="#6366f1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h1>AI Image Generator</h1>
                <p class="subtitle">Create stunning images in various artistic styles using AI</p>
                
                <div class="api-key-form">
                    <label for="api-key">OpenAI API Key</label>
                    <div class="input-group">
                        <input type="password" id="api-key" placeholder="sk-..." required>
                        <button type="button" id="toggle-api-key" class="toggle-btn">
                            <i data-feather="eye"></i>
                        </button>
                    </div>
                    <p class="help-text">Your API key is stored locally and never shared with anyone else</p>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="save-key" checked>
                        <label for="save-key">Save API key for future sessions</label>
                    </div>
                    
                    <button id="start-btn" class="primary-btn">
                        <i data-feather="arrow-right"></i>
                        Get Started
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Interface Screen -->
    <div id="main-screen" class="screen">
        <div class="container">
            <header class="app-header">
                <div class="header-content">
                    <h2>Generate Image</h2>
                    <div class="header-buttons">
                        <button id="history-btn" class="secondary-btn">
                            <i data-feather="clock"></i>
                            History
                        </button>
                        <button id="logout-btn" class="secondary-btn">
                            <i data-feather="log-out"></i>
                            Change API Key
                        </button>
                    </div>
                </div>
            </header>

            <div class="main-content">
                <div class="form-section">
                    <div class="controls-header">
                        <div class="controls-row">
                            <div class="style-section">
                                <label for="style-selector">Style</label>
                                <div class="custom-select" id="style-selector">
                                    <div class="select-button">
                                        <span class="select-text">Realistic</span>
                                        <i data-feather="chevron-down" class="select-arrow"></i>
                                    </div>
                                    <div class="select-dropdown">
                                        <div class="select-option active" data-style="realistic">
                                            <span>Realistic</span>
                                        </div>
                                        <div class="select-option" data-style="digital-illustration">
                                            <span>Digital Illustration</span>
                                        </div>
                                        <div class="select-option" data-style="watercolor">
                                            <span>Watercolor</span>
                                        </div>
                                        <div class="select-option" data-style="anime">
                                            <span>Anime/Manga</span>
                                        </div>
                                        <div class="select-option" data-style="pixel-art">
                                            <span>Pixel Art</span>
                                        </div>
                                        <div class="select-option" data-style="comic">
                                            <span>Comic Book</span>
                                        </div>
                                        <div class="select-option" data-style="fantasy">
                                            <span>Epic Fantasy</span>
                                        </div>
                                        <div class="select-option" data-style="sci-fi">
                                            <span>Science Fiction</span>
                                        </div>
                                        <div class="select-option" data-style="abstract">
                                            <span>Abstract Art</span>
                                        </div>
                                        <div class="select-option" data-style="oil-painting">
                                            <span>Oil Painting</span>
                                        </div>
                                        <div class="select-option" data-style="vintage">
                                            <span>Vintage/Retro</span>
                                        </div>
                                        <div class="select-option" data-style="monochrome">
                                            <span>Black & White</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="aspect-section">
                                <label for="aspect-selector">Aspect Ratio</label>
                                <div class="custom-select" id="aspect-selector">
                                    <div class="select-button">
                                        <span class="select-text">Square (1:1)</span>
                                        <i data-feather="chevron-down" class="select-arrow"></i>
                                    </div>
                                    <div class="select-dropdown">
                                        <div class="select-option active" data-aspect="square" data-size="1024x1024">
                                            <span>Square (1:1)</span>
                                        </div>
                                        <div class="select-option" data-aspect="portrait" data-size="1024x1792">
                                            <span>Portrait (9:16)</span>
                                        </div>
                                        <div class="select-option" data-aspect="landscape" data-size="1792x1024">
                                            <span>Landscape (16:9)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="input-section">
                        <label for="prompt">What do you want to draw? <span class="required">Required</span></label>
                        <div class="prompt-container">
                            <textarea id="prompt" placeholder="Describe in detail the image you want to create..." rows="4" required></textarea>
                            <button id="enhance-prompt-btn" class="enhance-btn" title="Enhance prompt with AI">
                                <i data-feather="zap"></i>
                            </button>
                        </div>
                        <div class="char-count">
                            <span id="char-count">0</span>/400
                        </div>
                    </div>

                    <button id="generate-btn" class="generate-btn">
                        <i data-feather="image"></i>
                        Generate
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Screen -->
    <div id="results-screen" class="screen">
        <div class="container">
            <header class="app-header">
                <div class="header-content">
                    <h2>Generated Image</h2>
                    <button id="back-btn" class="secondary-btn">
                        <i data-feather="arrow-left"></i>
                        Generate Another
                    </button>
                </div>
            </header>

            <div class="results-content">
                <div class="image-container">
                    <div id="loading-state" class="loading-state">
                        <div class="spinner"></div>
                        <p>Generating your image...</p>
                    </div>
                    
                    <div id="image-result" class="image-result hidden">
                        <img id="generated-image" alt="Generated image">
                        <div class="image-actions">
                            <button id="download-btn" class="primary-btn">
                                <i data-feather="download"></i>
                                Download Image
                            </button>
                        </div>
                    </div>

                    <div id="error-state" class="error-state hidden">
                        <div class="error-icon">
                            <i data-feather="alert-circle"></i>
                        </div>
                        <h3>Generation Failed</h3>
                        <p id="error-message">Something went wrong while generating your image.</p>
                        <button id="retry-btn" class="secondary-btn">
                            <i data-feather="refresh-cw"></i>
                            Try Again
                        </button>
                    </div>
                </div>

                <div class="prompt-info">
                    <h4>Prompt Used</h4>
                    <p id="used-prompt"></p>
                    <div class="style-info">
                        <span>Style: </span>
                        <span id="used-style"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- History Screen -->
    <div id="history-screen" class="screen">
        <div class="container">
            <header class="app-header">
                <div class="header-content">
                    <h2>Image History</h2>
                    <div class="header-buttons">
                        <button id="clear-history-btn" class="secondary-btn">
                            <i data-feather="trash-2"></i>
                            Clear History
                        </button>
                        <button id="back-from-history-btn" class="secondary-btn">
                            <i data-feather="arrow-left"></i>
                            Back
                        </button>
                    </div>
                </div>
            </header>

            <div class="history-content">
                <div id="history-grid" class="history-grid">
                    <!-- History items will be dynamically inserted here -->
                </div>
                <div id="history-empty" class="history-empty hidden">
                    <div class="empty-icon">
                        <i data-feather="image"></i>
                    </div>
                    <h3>No Images Yet</h3>
                    <p>Generate your first image to see it appear here</p>
                    <button id="start-generating-btn" class="primary-btn">
                        <i data-feather="plus"></i>
                        Start Generating
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/feather-icons"></script>
    <script src="script.js"></script>
</body>
</html>
