:root {
    --primary-color: #6366f1;
    --primary-hover: #5b21b6;
    --secondary-color: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --background: #ffffff;
    --surface: #f8fafc;
    --error: #ef4444;
    --success: #10b981;
    --warning: #f59e0b;
    
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

/* Screen Management */
.screen {
    display: none;
    min-height: 100vh;
}

.screen.active {
    display: block;
}

/* Welcome Screen */
.welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    text-align: center;
    max-width: 480px;
    margin: 0 auto;
}

.logo {
    margin-bottom: 2rem;
}

h1 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.api-key-form {
    width: 100%;
    max-width: 400px;
}

.api-key-form label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.input-group {
    position: relative;
    margin-bottom: 0.75rem;
}

.input-group input {
    width: 100%;
    padding: 0.875rem 3rem 0.875rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    transition: all 0.2s ease;
    background-color: var(--background);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.toggle-btn {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.toggle-btn:hover {
    color: var(--text-secondary);
}

.help-text {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkbox-group label {
    font-size: 0.875rem;
    font-weight: 400;
    margin: 0;
    cursor: pointer;
}

/* Buttons */
.primary-btn, .secondary-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    outline: none;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.primary-btn:hover {
    background-color: var(--primary-hover);
    box-shadow: var(--shadow);
}

.primary-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.secondary-btn {
    background-color: var(--surface);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.secondary-btn:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
}

/* Main Screen */
.app-header {
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

h2 {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.main-content {
    max-width: 800px;
    margin: 0 auto;
}

.form-section {
    background: var(--surface);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.input-section {
    margin-bottom: 2rem;
}

.input-section label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    min-height: 120px;
    transition: all 0.2s ease;
    background-color: var(--background);
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

/* Controls Header */
.controls-header {
    margin-bottom: 2rem;
}

.controls-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.style-section,
.aspect-section {
    display: flex;
    flex-direction: column;
}

.style-section label,
.aspect-section label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Custom Select Dropdown */
.custom-select {
    position: relative;
}

.select-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    background-color: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
}

.select-button:hover {
    border-color: var(--text-secondary);
}

.select-button.open {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgb(99 102 241 / 0.1);
}

.select-text {
    font-weight: 400;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.select-arrow {
    color: var(--text-muted);
    transition: transform 0.2s ease;
    width: 16px;
    height: 16px;
}

.select-button.open .select-arrow {
    transform: rotate(180deg);
}

.select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-lg);
    z-index: 100;
    max-height: 300px;
    overflow-y: auto;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.select-dropdown.open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.select-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.select-option:last-child {
    border-bottom: none;
}

.select-option:hover {
    background-color: var(--surface);
}

.select-option.active {
    background-color: rgb(99 102 241 / 0.05);
    color: var(--primary-color);
}

.option-icon {
    font-size: 1.125rem;
    flex-shrink: 0;
}

.select-option span:last-child {
    font-weight: 500;
}

/* Prompt Container */
.prompt-container {
    position: relative;
    display: flex;
    align-items: stretch;
}

.prompt-container textarea {
    flex: 1;
    padding-right: 3rem;
    margin-bottom: 0;
}

.enhance-btn {
    position: absolute;
    right: 0.75rem;
    top: 0.75rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.enhance-btn:hover {
    color: var(--primary-color);
    background-color: var(--surface);
}

.enhance-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    animation: pulse 1.5s ease-in-out infinite;
}

.char-count {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-align: right;
    margin-top: 0.5rem;
}

.required {
    color: var(--error);
    font-size: 0.75rem;
    font-weight: 400;
}

/* Generate Button */
.generate-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
    margin-top: 1.5rem;
}

.generate-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.generate-btn:active {
    transform: translateY(0);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.secondary-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    animation: pulse 1.5s ease-in-out infinite;
}

/* Auto-resize textarea */
textarea {
    resize: none;
    overflow: hidden;
    transition: height 0.2s ease;
}

/* Enhanced shadows and hover effects */
.custom-select:hover .select-button {
    box-shadow: var(--shadow-sm);
}

.select-dropdown {
    border-top: none;
    margin-top: -1px;
}

/* Better focus states */
.select-button:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading state improvements */
.loading-state p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Enhanced button states */
.primary-btn:active,
.secondary-btn:active {
    transform: translateY(1px);
    transition: transform 0.1s ease;
}

/* Improved image container */
.image-result img {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.image-result img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Results Screen */
.results-content {
    max-width: 800px;
    margin: 0 auto;
    display: grid;
    gap: 2rem;
}

.image-container {
    background: var(--surface);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    text-align: center;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 3rem 1rem;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.image-result {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.image-result img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow);
}

.image-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 3rem 1rem;
    color: var(--text-secondary);
}

.error-icon {
    color: var(--error);
    font-size: 2rem;
}

.error-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.error-state p {
    text-align: center;
    max-width: 400px;
}

.prompt-info {
    background: var(--surface);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.prompt-info h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.prompt-info p {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.style-info {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.style-info span:first-child {
    font-weight: 500;
}

.style-info span:last-child {
    color: var(--text-secondary);
}

/* History Screen Styles */
.header-buttons {
    display: flex;
    gap: 0.75rem;
}

.history-content {
    padding: 2rem 0;
}

.history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.history-item {
    background: var(--card-bg);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.history-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.history-item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: var(--bg-secondary);
}

.history-item-info {
    padding: 1rem;
}

.history-item-prompt {
    font-size: 0.9rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
}

.history-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.history-item-style {
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 500;
}

.history-item-date {
    font-size: 0.75rem;
}

.history-empty {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem;
    background: var(--bg-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
}

.empty-icon i {
    width: 32px;
    height: 32px;
}

.history-empty h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.history-empty p {
    margin-bottom: 2rem;
    color: var(--text-secondary);
}

.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .controls-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-section,
    .image-container,
    .prompt-info {
        padding: 1.5rem;
    }
    
    .results-content {
        gap: 1.5rem;
    }

    .generate-btn {
        padding: 1rem;
        font-size: 1rem;
    }

    .enhance-btn {
        right: 0.5rem;
        top: 0.5rem;
        padding: 0.375rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.75rem;
    }
    
    .form-section {
        padding: 1rem;
    }

    .controls-header {
        margin-bottom: 1.5rem;
    }
    
    .primary-btn,
    .secondary-btn,
    .generate-btn {
        width: 100%;
        justify-content: center;
    }
    
    .image-actions {
        flex-direction: column;
    }

    .prompt-container textarea {
        padding-right: 2.5rem;
        font-size: 1rem;
    }

    .select-button {
        padding: 0.625rem 0.75rem;
        min-height: 40px;
    }

    .select-text {
        font-size: 0.8rem;
    }
}
