# AI Image Generator

Una aplicación web moderna y responsiva para generar imágenes de alta calidad usando la API de OpenAI DALL-E 3.

## Características

- ✨ **Generación de imágenes de alta calidad** usando DALL-E 3
- 🎨 **12 estilos artísticos diferentes** (Realista, Ilustración Digital, Acuarela, Anime, etc.)
- 📐 **Múltiples relaciones de aspecto** (Cuadrado, Retrato, Paisaje)
- 🔧 **Mejora automática de prompts** con IA
- 📱 **Diseño completamente responsivo**
- 💾 **Historial de imágenes generadas**
- ⬇️ **Descarga directa de imágenes**
- 🔄 **Reutilización de prompts del historial**

## Requisitos

- Una clave API válida de OpenAI con acceso a DALL-E 3
- Navegador web moderno con soporte para JavaScript ES6+

## Instalación y Uso

1. **Clona o descarga** este repositorio
2. **Abre** `index.html` en tu navegador web
3. **Ingresa tu clave API** de OpenAI (comienza con `sk-`)
4. **¡Comienza a generar imágenes!**

### Obtener una clave API de OpenAI

1. Ve a [platform.openai.com](https://platform.openai.com)
2. Crea una cuenta o inicia sesión
3. Ve a la sección "API Keys"
4. Crea una nueva clave API
5. Asegúrate de tener créditos disponibles para usar DALL-E 3

## Estilos Disponibles

- **Realista**: Fotografías profesionales de alta calidad
- **Ilustración Digital**: Arte digital moderno con colores vibrantes
- **Acuarela**: Pinturas con técnica de acuarela tradicional
- **Anime/Manga**: Estilo de animación japonesa
- **Pixel Art**: Arte retro de 16-bit
- **Cómic**: Estilo de libro de cómics con líneas dinámicas
- **Fantasía Épica**: Arte fantástico con atmósfera mágica
- **Ciencia Ficción**: Diseños futuristas y cyberpunk
- **Arte Abstracto**: Composiciones modernas y contemporáneas
- **Pintura al Óleo**: Técnica clásica de bellas artes
- **Vintage/Retro**: Estética nostálgica y antigua
- **Blanco y Negro**: Fotografía monocromática dramática

## Consejos para Mejores Resultados

1. **Sé específico**: Describe detalles como iluminación, colores, composición
2. **Usa el botón de mejora**: La IA puede expandir tu prompt básico
3. **Experimenta con estilos**: Cada estilo produce resultados únicos
4. **Prueba diferentes relaciones de aspecto**: Según el tipo de imagen que quieras

## Características Técnicas

- **Modelo**: DALL-E 3 de OpenAI
- **Calidad**: HD (alta definición)
- **Resoluciones soportadas**:
  - Cuadrado: 1024x1024
  - Retrato: 1024x1792
  - Paisaje: 1792x1024

## Privacidad y Seguridad

- Tu clave API se almacena localmente en tu navegador
- No se envía información a servidores externos (excepto OpenAI)
- Puedes elegir si guardar o no tu clave API para futuras sesiones

## Solución de Problemas

### Error de clave API inválida
- Verifica que tu clave comience con `sk-`
- Asegúrate de tener créditos disponibles en tu cuenta de OpenAI
- Confirma que tu clave tenga acceso a DALL-E 3

### Error de límite de velocidad
- Espera unos momentos antes de intentar nuevamente
- DALL-E 3 tiene límites de uso por minuto

### Error de política de contenido
- Modifica tu prompt para evitar contenido prohibido
- Consulta las políticas de uso de OpenAI

## Tecnologías Utilizadas

- HTML5
- CSS3 (Variables CSS, Grid, Flexbox)
- JavaScript ES6+ (Fetch API, Async/Await)
- Feather Icons
- Google Fonts (Inter)

## Licencia

Este proyecto es de código abierto. Puedes usarlo, modificarlo y distribuirlo libremente.

## Contribuciones

Las contribuciones son bienvenidas. Si encuentras errores o tienes sugerencias de mejora, por favor abre un issue o envía un pull request.
