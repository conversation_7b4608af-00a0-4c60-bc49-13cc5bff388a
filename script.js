class ImageGenerator {
    constructor() {
        this.apiKey = '';
        this.currentPrompt = '';
        this.currentStyle = 'realistic';
        this.currentAspect = 'square';
        this.currentSize = '1024x1024';
        this.generatedImageUrl = '';
        this.imageHistory = [];
        
        this.init();
    }

    init() {
        this.loadSavedApiKey();
        this.loadImageHistory();
        this.bindEvents();
        this.initializeFeatherIcons();
        this.updateCharacterCount();
    }

    initializeFeatherIcons() {
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    }

    loadSavedApiKey() {
        const savedKey = localStorage.getItem('openai_api_key');
        if (savedKey) {
            document.getElementById('api-key').value = savedKey;
            document.getElementById('save-key').checked = true;
        }
    }

    loadImageHistory() {
        const savedHistory = localStorage.getItem('image_history');
        if (savedHistory) {
            try {
                this.imageHistory = JSON.parse(savedHistory);
                this.updateHistoryCounter();
            } catch (error) {
                console.error('Failed to load image history:', error);
                this.imageHistory = [];
            }
        }
    }

    saveImageToHistory(imageUrl, prompt, style, aspectRatio) {
        const historyItem = {
            id: Date.now(),
            imageUrl,
            prompt,
            style,
            aspectRatio,
            timestamp: new Date().toISOString(),
            size: this.currentSize
        };

        this.imageHistory.unshift(historyItem);
        
        // Keep only the last 50 images to prevent localStorage from getting too large
        if (this.imageHistory.length > 50) {
            this.imageHistory = this.imageHistory.slice(0, 50);
        }

        localStorage.setItem('image_history', JSON.stringify(this.imageHistory));
        this.updateHistoryCounter();
    }

    updateHistoryCounter() {
        const historyBtn = document.getElementById('history-btn');
        if (historyBtn) {
            const count = this.imageHistory.length;
            const countText = count > 0 ? ` (${count})` : '';
            historyBtn.innerHTML = `<i data-feather="clock"></i> History${countText}`;
            
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }
    }

    clearImageHistory() {
        this.imageHistory = [];
        localStorage.removeItem('image_history');
        this.updateHistoryCounter();
        this.renderHistoryView();
    }

    bindEvents() {
        // Welcome screen events
        document.getElementById('toggle-api-key').addEventListener('click', this.toggleApiKeyVisibility);
        document.getElementById('start-btn').addEventListener('click', this.handleStart.bind(this));
        document.getElementById('api-key').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleStart();
            }
        });

        // Main screen events
        document.getElementById('logout-btn').addEventListener('click', this.handleLogout.bind(this));
        document.getElementById('history-btn').addEventListener('click', this.handleViewHistory.bind(this));
        document.getElementById('generate-btn').addEventListener('click', this.handleGenerate.bind(this));
        document.getElementById('enhance-prompt-btn').addEventListener('click', this.handleEnhancePrompt.bind(this));
        
        // Prompt character counter
        const promptInput = document.getElementById('prompt');
        promptInput.addEventListener('input', this.updateCharacterCount.bind(this));
        promptInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                this.handleGenerate();
            }
        });

        // Custom select events
        this.initCustomSelects();

        // Results screen events
        document.getElementById('back-btn').addEventListener('click', this.handleBack.bind(this));
        document.getElementById('download-btn').addEventListener('click', this.handleDownload.bind(this));
        document.getElementById('retry-btn').addEventListener('click', this.handleRetry.bind(this));
        
        // History screen events
        document.getElementById('back-from-history-btn').addEventListener('click', this.handleBackFromHistory.bind(this));
        document.getElementById('clear-history-btn').addEventListener('click', this.handleClearHistory.bind(this));
        document.getElementById('start-generating-btn').addEventListener('click', this.handleStartGenerating.bind(this));

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.custom-select')) {
                this.closeAllDropdowns();
            }
        });
    }

    updateCharacterCount() {
        const promptInput = document.getElementById('prompt');
        const charCount = document.getElementById('char-count');
        const count = promptInput.value.length;
        charCount.textContent = count;
        
        // Change color if approaching limit
        if (count > 350) {
            charCount.style.color = 'var(--error)';
        } else if (count > 300) {
            charCount.style.color = 'var(--warning)';
        } else {
            charCount.style.color = 'var(--text-muted)';
        }
    }

    toggleApiKeyVisibility() {
        const input = document.getElementById('api-key');
        const icon = document.querySelector('#toggle-api-key i');
        
        if (input && icon) {
            if (input.type === 'password') {
                input.type = 'text';
                icon.setAttribute('data-feather', 'eye-off');
            } else {
                input.type = 'password';
                icon.setAttribute('data-feather', 'eye');
            }
            
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }
    }

    handleStart() {
        const apiKeyInput = document.getElementById('api-key');
        const saveKey = document.getElementById('save-key').checked;
        
        if (!apiKeyInput.value.trim()) {
            this.showError('Please enter your OpenAI API key');
            apiKeyInput.focus();
            return;
        }

        if (!apiKeyInput.value.startsWith('sk-')) {
            this.showError('Please enter a valid OpenAI API key (starts with sk-)');
            apiKeyInput.focus();
            return;
        }

        this.apiKey = apiKeyInput.value.trim();
        
        if (saveKey) {
            localStorage.setItem('openai_api_key', this.apiKey);
        } else {
            localStorage.removeItem('openai_api_key');
        }

        this.showScreen('main-screen');
        document.getElementById('prompt').focus();
    }

    handleLogout() {
        this.apiKey = '';
        document.getElementById('api-key').value = '';
        localStorage.removeItem('openai_api_key');
        this.showScreen('welcome-screen');
        document.getElementById('api-key').focus();
    }

    initCustomSelects() {
        // Style selector
        const styleSelector = document.getElementById('style-selector');
        const styleButton = styleSelector.querySelector('.select-button');
        const styleDropdown = styleSelector.querySelector('.select-dropdown');
        
        styleButton.addEventListener('click', () => {
            this.toggleDropdown(styleSelector);
        });
        
        styleSelector.querySelectorAll('.select-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.selectStyleOption(e.currentTarget);
                this.closeDropdown(styleSelector);
            });
        });

        // Aspect selector
        const aspectSelector = document.getElementById('aspect-selector');
        const aspectButton = aspectSelector.querySelector('.select-button');
        const aspectDropdown = aspectSelector.querySelector('.select-dropdown');
        
        aspectButton.addEventListener('click', () => {
            this.toggleDropdown(aspectSelector);
        });
        
        aspectSelector.querySelectorAll('.select-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.selectAspectOption(e.currentTarget);
                this.closeDropdown(aspectSelector);
            });
        });
    }

    toggleDropdown(selector) {
        const button = selector.querySelector('.select-button');
        const dropdown = selector.querySelector('.select-dropdown');
        
        const isOpen = button.classList.contains('open');
        
        // Close all other dropdowns
        this.closeAllDropdowns();
        
        if (!isOpen) {
            button.classList.add('open');
            dropdown.classList.add('open');
        }
    }

    closeDropdown(selector) {
        const button = selector.querySelector('.select-button');
        const dropdown = selector.querySelector('.select-dropdown');
        
        button.classList.remove('open');
        dropdown.classList.remove('open');
    }

    closeAllDropdowns() {
        document.querySelectorAll('.custom-select').forEach(selector => {
            this.closeDropdown(selector);
        });
    }

    selectStyleOption(option) {
        const selector = document.getElementById('style-selector');
        const button = selector.querySelector('.select-button');
        const text = button.querySelector('.select-text');
        
        // Remove active from all options
        selector.querySelectorAll('.select-option').forEach(opt => {
            opt.classList.remove('active');
        });
        
        // Add active to selected option
        option.classList.add('active');
        
        // Update button display
        text.textContent = option.querySelector('span').textContent;
        
        // Update current style
        this.currentStyle = option.dataset.style;
    }

    selectAspectOption(option) {
        const selector = document.getElementById('aspect-selector');
        const button = selector.querySelector('.select-button');
        const text = button.querySelector('.select-text');
        
        // Remove active from all options
        selector.querySelectorAll('.select-option').forEach(opt => {
            opt.classList.remove('active');
        });
        
        // Add active to selected option
        option.classList.add('active');
        
        // Update button display
        text.textContent = option.querySelector('span').textContent;
        
        // Update current aspect and size
        this.currentAspect = option.dataset.aspect;
        this.currentSize = option.dataset.size;
    }

    async handleGenerate() {
        const promptInput = document.getElementById('prompt');
        const prompt = promptInput.value.trim();
        
        if (!prompt) {
            this.showError('Please enter a description for your image');
            promptInput.focus();
            return;
        }

        this.currentPrompt = prompt;
        this.showScreen('results-screen');
        this.showLoadingState();

        try {
            const imageUrl = await this.generateImage(prompt, this.currentStyle);
            this.generatedImageUrl = imageUrl;
            this.showImageResult(imageUrl);
        } catch (error) {
            console.error('Image generation failed:', error);
            this.showErrorState(error.message);
        }
    }

    async handleEnhancePrompt() {
        const promptInput = document.getElementById('prompt');
        const currentPrompt = promptInput.value.trim();
        
        if (!currentPrompt) {
            this.showError('Por favor ingresa una descripción antes de mejorarla');
            promptInput.focus();
            return;
        }

        const enhanceBtn = document.getElementById('enhance-prompt-btn');
        const originalText = enhanceBtn.innerHTML;
        
        try {
            enhanceBtn.innerHTML = '<i data-feather="loader"></i>';
            enhanceBtn.disabled = true;
            
            if (typeof feather !== 'undefined') {
                feather.replace();
            }

            const enhancedPrompt = await this.enhancePromptWithAI(currentPrompt);
            promptInput.value = enhancedPrompt;
            this.updateCharacterCount();
            
            // Auto-resize textarea if needed
            promptInput.style.height = 'auto';
            promptInput.style.height = promptInput.scrollHeight + 'px';
            
        } catch (error) {
            console.error('Prompt enhancement failed:', error);
            this.showError('Failed to enhance prompt. Please try again.');
        } finally {
            enhanceBtn.innerHTML = originalText;
            enhanceBtn.disabled = false;
            
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }
    }

    async enhancePromptWithAI(originalPrompt) {
        const headers = {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.apiKey}`
        };

        const data = {
            model: 'gpt-4o', // the newest OpenAI model which was released May 13, 2024. do not change this unless explicitly requested by the user
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert at creating detailed prompts for AI image generation. Your job is to take a basic prompt and expand it with specific visual details, artistic techniques, lighting, composition, and style elements to create high-quality images. Keep the original idea but add elements that enhance the visual outcome. Respond only with the improved prompt, no additional explanations. Maintain a professional and technical style.'
                },
                {
                    role: 'user',
                    content: `Enhance this prompt for high-quality image generation: "${originalPrompt}"`
                }
            ],
            max_tokens: 200,
            temperature: 0.7
        };

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.status === 200) {
            if (!result.choices || !result.choices[0] || !result.choices[0].message) {
                throw new Error('Invalid response from OpenAI API');
            }
            return result.choices[0].message.content.trim();
        } else {
            let errorMessage = 'Failed to enhance prompt';
            
            if (response.status === 401) {
                errorMessage = 'Invalid API key. Please check your OpenAI API key.';
            } else if (response.status === 429) {
                errorMessage = 'Rate limit exceeded. Please try again later.';
            } else if (result.error?.message) {
                errorMessage = result.error.message;
            }
            
            throw new Error(errorMessage);
        }
    }

    async generateImage(prompt, style) {
        const stylePrompts = {
            'realistic': 'photorealistic, high quality, detailed, professional photography',
            'digital-illustration': 'digital art, illustration, vibrant colors, clean lines',
            'watercolor': 'watercolor painting, soft brushstrokes, artistic, flowing colors',
            'anime': 'anime style, manga art, Japanese animation, detailed',
            'pixel-art': 'pixel art, 8-bit style, retro gaming, crisp pixels',
            'comic': 'comic book style, bold lines, pop art, dynamic',
            'fantasy': 'epic fantasy, magical, mystical atmosphere, detailed',
            'sci-fi': 'science fiction, futuristic, cyberpunk, high-tech',
            'abstract': 'abstract art, modern, contemporary, artistic',
            'oil-painting': 'oil painting, classical art, fine brushwork, textured',
            'vintage': 'vintage style, retro-futuristic, nostalgic, aged',
            'monochrome': 'black and white, monochrome, high contrast, dramatic'
        };

        const stylePrefix = stylePrompts[style] || '';
        const fullPrompt = `${prompt}, ${stylePrefix}, high quality, masterpiece`;

        const headers = {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${this.apiKey}`
        };

        const data = {
            prompt: fullPrompt,
            n: 1,
            size: this.currentSize,
            quality: "high",
            model: "gpt-image-1"
        };

        const response = await fetch("https://api.openai.com/v1/images/generations", {
            method: "POST",
            headers: headers,
            body: JSON.stringify(data)
        });

        const result = await response.json();
        
        if (response.status === 200) {
            if (!result.data || !result.data[0] || !result.data[0].url) {
                throw new Error('Invalid response from OpenAI API');
            }
            return result.data[0].url;
        } else {
            let errorMessage = 'Failed to generate image';
            
            if (response.status === 401) {
                errorMessage = 'Invalid API key. Please check your OpenAI API key.';
            } else if (response.status === 429) {
                errorMessage = 'Rate limit exceeded. Please try again later.';
            } else if (response.status === 400) {
                errorMessage = result.error?.message || 'Invalid request. Please try a different prompt.';
            } else if (result.error?.message) {
                errorMessage = result.error.message;
            }
            
            throw new Error(errorMessage);
        }
    }

    handleBack() {
        this.showScreen('main-screen');
    }

    async handleDownload() {
        if (!this.generatedImageUrl) {
            this.showError('No image to download');
            return;
        }

        const downloadBtn = document.getElementById('download-btn');
        const originalText = downloadBtn.innerHTML;

        try {
            downloadBtn.innerHTML = '<i data-feather="loader"></i> Downloading...';
            downloadBtn.disabled = true;
            
            if (typeof feather !== 'undefined') {
                feather.replace();
            }

            // Use fetch with proper headers to handle CORS
            const response = await fetch(this.generatedImageUrl, {
                method: 'GET',
                mode: 'cors',
                cache: 'no-cache'
            });
            
            if (!response.ok) {
                throw new Error('Failed to fetch image');
            }
            
            const blob = await response.blob();
            
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            
            // Generate filename with timestamp and style
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
            const styleName = this.getStyleDisplayName(this.currentStyle).replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            a.download = `ai-image-${styleName}-${timestamp}.png`;
            
            // Trigger download
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
        } catch (error) {
            console.error('Download failed:', error);
            this.showError('Failed to download image. Please try right-clicking and saving the image manually.');
        } finally {
            downloadBtn.innerHTML = originalText;
            downloadBtn.disabled = false;
            
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }
    }

    handleRetry() {
        this.handleGenerate();
    }

    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }

    showLoadingState() {
        document.getElementById('loading-state').classList.remove('hidden');
        document.getElementById('image-result').classList.add('hidden');
        document.getElementById('error-state').classList.add('hidden');
        
        // Update prompt info
        document.getElementById('used-prompt').textContent = this.currentPrompt;
        document.getElementById('used-style').textContent = `${this.getStyleDisplayName(this.currentStyle)} • ${this.getAspectDisplayName(this.currentAspect)}`;
    }

    showImageResult(imageUrl) {
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('error-state').classList.add('hidden');
        document.getElementById('image-result').classList.remove('hidden');
        
        const img = document.getElementById('generated-image');
        img.src = imageUrl;
        img.alt = `Generated image: ${this.currentPrompt}`;
    }

    showErrorState(errorMessage) {
        document.getElementById('loading-state').classList.add('hidden');
        document.getElementById('image-result').classList.add('hidden');
        document.getElementById('error-state').classList.remove('hidden');
        
        document.getElementById('error-message').textContent = errorMessage;
    }

    getStyleDisplayName(style) {
        const styleNames = {
            'realistic': 'Realistic',
            'digital-illustration': 'Digital Illustration',
            'watercolor': 'Watercolor',
            'anime': 'Anime/Manga',
            'pixel-art': 'Pixel Art',
            'comic': 'Comic Book',
            'fantasy': 'Epic Fantasy',
            'sci-fi': 'Science Fiction',
            'abstract': 'Abstract Art',
            'oil-painting': 'Oil Painting',
            'vintage': 'Vintage/Retro',
            'monochrome': 'Black & White'
        };
        return styleNames[style] || style;
    }

    getAspectDisplayName(aspect) {
        const aspectNames = {
            'square': 'Square (1:1)',
            'portrait': 'Portrait (9:16)',
            'landscape': 'Landscape (16:9)'
        };
        return aspectNames[aspect] || aspect;
    }

    showError(message) {
        // Simple error notification - could be enhanced with a proper toast system
        alert(message);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ImageGenerator();
});
