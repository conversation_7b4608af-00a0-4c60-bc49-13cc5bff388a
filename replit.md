# AI Image Generator

## Overview

This is a client-side web application that generates AI images using OpenAI's API. The app is built with vanilla HTML, CSS, and JavaScript without any frameworks, providing a clean and modern interface for creating images in various artistic styles. The application stores the user's API key locally and handles all operations in the browser without requiring a backend server.

## User Preferences

Preferred communication style: Simple, everyday language.
Interface language: English for user-facing elements
Prefer modern, minimalist UI components over traditional form elements
Use gpt-image-1 model for image generation
Minimalist design similar to reference image provided

## System Architecture

### Frontend Architecture
- **Pure Client-Side Application**: Built entirely with vanilla HTML5, CSS3, and JavaScript
- **Single Page Application (SPA)**: Uses screen-based navigation with JavaScript to switch between views
- **No Framework Dependencies**: Deliberately avoids React, Vue, Angular, or other frontend frameworks
- **Local Storage Strategy**: Utilizes browser localStorage for API key persistence and session management

### Design Philosophy
- **Minimalist UI/UX**: Clean, modern design with subtle shadows, rounded corners, and generous spacing
- **Responsive Design**: Mobile-first approach that adapts to desktop, tablet, and mobile devices
- **Typography**: Uses Inter font family for clean, readable text
- **Color Scheme**: Light theme with soft colors, focusing on accessibility and visual hierarchy

## Key Components

### 1. Welcome Screen (`index.html`)
- **API Key Management**: Secure input field for OpenAI API key with visibility toggle
- **Local Storage Integration**: Option to save API key for future sessions
- **User Onboarding**: Clean introduction with logo and explanatory text

### 2. Advanced Image Generator (`script.js`)
- **ImageGenerator Class**: Enhanced application controller with minimalist UI components
- **Custom Dropdown System**: Clean style and aspect ratio selectors with smooth animations
- **AI-Powered Prompt Enhancement**: Built-in GPT-4o integration to improve user prompts automatically
- **Multi-Format Support**: Supports square (1:1), portrait (9:16), and landscape (16:9) aspect ratios
- **HD Image Generation**: Uses gpt-image-1 model with HD quality settings for superior image output
- **Enhanced Download Function**: Improved file naming and CORS handling for reliable downloads
- **Character Counter**: Real-time prompt length tracking with visual feedback
- **Minimalist Interface**: Clean English interface following reference design principles

### 3. Styling System (`styles.css`)
- **CSS Custom Properties**: Centralized design tokens for colors, spacing, and typography
- **Modern CSS Features**: Uses flexbox/grid layouts, CSS variables, and smooth transitions
- **Component-Based Styling**: Modular CSS architecture for maintainability

## Data Flow

1. **User Authentication**: User enters OpenAI API key on welcome screen
2. **Key Validation**: API key is validated and optionally stored in localStorage
3. **Style Selection**: User chooses from predefined artistic styles
4. **Prompt Input**: User enters text description for image generation
5. **API Request**: Direct browser-to-OpenAI API communication
6. **Image Display**: Generated image is displayed in the interface
7. **Download Option**: Users can download generated images directly

## External Dependencies

### Third-Party Services
- **OpenAI API**: Primary service for image generation using DALL-E
- **Google Fonts**: Inter font family for typography
- **Feather Icons**: Lightweight icon library for UI elements

### JavaScript Libraries
- **OpenAI SDK**: Version 5.10.2 for API communication
- **Feather Icons**: For consistent iconography throughout the interface

### CDN Resources
- Google Fonts API for web fonts
- Feather Icons CDN for scalable vector icons

## Deployment Strategy

### Static Hosting
- **No Server Required**: Application can be deployed to any static hosting service
- **CDN Friendly**: All assets can be cached for optimal performance
- **Environment Agnostic**: Works on any web server that can serve static files

### Security Considerations
- **API Key Security**: Keys are stored client-side only, never transmitted to third-party servers
- **HTTPS Required**: Must be served over HTTPS for localStorage security and API access
- **CORS Handling**: Direct browser-to-OpenAI API communication requires proper CORS configuration

### Performance Optimization
- **Minimal Dependencies**: Only essential libraries included to reduce bundle size
- **Local Storage**: Reduces repeated API key entry and improves user experience
- **Responsive Images**: Generated images are displayed efficiently across devices

## Development Notes

The application follows a progressive enhancement approach, starting with a solid HTML foundation and layering on CSS and JavaScript functionality. The codebase is structured for easy maintenance and future feature additions while maintaining the no-framework philosophy.

Key architectural decisions prioritize user privacy (local API key storage), performance (minimal dependencies), and accessibility (semantic HTML and responsive design).